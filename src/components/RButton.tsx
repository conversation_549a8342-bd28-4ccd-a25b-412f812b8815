import { Button } from 'antd';
import { ButtonProps } from 'antd/es/button';
import clsx from 'clsx';

type CustomButtonProps = {
  color?:
    | 'default'
    | 'primary'
    | 'danger'
    | 'purple'
    | 'green'
    | 'red'
    | 'cyan'
    | 'success'
    | 'orange';
};

interface RButtonProps extends Omit<ButtonProps, 'color'>, CustomButtonProps {
  children: React.ReactNode;
  type?: 'primary' | 'dashed' | 'link' | 'text' | 'default';
}

const successClass = 'btn-success bg-green-500 text-white hover:bg-green-600';
export const RButton = ({
  children,
  className,
  type = 'primary',
  color = 'primary',
  ...props
}: RButtonProps) => {
  return (
    <Button
      type={type}
      className={clsx('w-fit', color === 'success' && successClass, className)}
      color={color as ButtonProps['color']}
      {...props}
    >
      {children}
    </Button>
  );
};

export default RButton;
