export enum BalanceAdjustmentChangeType {
  INCREASE = 1,
  DECREASE = 2
}

export type BalanceAdjustment = {
  id: number;
  account: string;
  name: string; // 暱稱
  currency: string; // 幣別
  changeType: BalanceAdjustmentChangeType; // 1: 調整類型1, 2: 調整類型2
  amount: number; // 調整金額
  remain: number; // 剩餘點數
  note: string; // 備註
  createdBy: string; // 操作管理員
  createdAt: number; // 建立時間
};

export type BalanceAdjustmentSearchParams = {
  timeStart?: number; // 開始時間
  timeEnd?: number; // 結束時間
  account?: string; // 玩家帳號
  createdBy?: string; // 操作管理員
  changeType?: number; // 調整類型
  excludeTest?: number; // 排除測試帳號 1: 排除, 0: 包含
  page: number;
  limit: number;
};
