import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { getBalanceAdjustmentList } from '@/api/balanceAdjustment';
import QuickDateSelect from '@/components/QuickDateSelect';
import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import RSelect from '@/components/RSelect';
import RTable from '@/components/RTable';
import SearchForm from '@/components/SearchForm';
import usePagination from '@/hooks/usePagination';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { BalanceAdjustmentSearchParams } from '@/types/balanceAdjustment';
import { numberFormat } from '@/utils/numbers';
import { formatTime } from '@/utils/time';
import { cleanSearchParams } from '@/utils/object';

type SearchFormValues = {
  changeType?: number;
  excludeTest?: number;
  conditionType?: (typeof CONDITION_OPTIONS)[number]['value'];
  conditionValue?: string;
  start: number;
  end: number;
};

const CONDITION_OPTIONS = [
  { label: 'pages_transaction_balanceAdjustment_account', value: 'account' },
  { label: 'pages_transaction_balanceAdjustment_createdBy', value: 'created_by' }
] as const;

const CHANGE_TYPE_OPTIONS = [
  { label: 'common_all', value: 'all' },
  { label: 'pages_transaction_balanceAdjustment_changeType_increase', value: 1 },
  { label: 'pages_transaction_balanceAdjustment_changeType_decrease', value: 2 }
] as const;

const CURRENCY_OPTIONS = [
  { label: 'pages_transaction_transactionRecord_currency_main', value: 'main' },
  { label: 'pages_transaction_transactionRecord_currency_secondary', value: 'secondary' }
] as const;

const defaultToday = [dayjs().startOf('day').valueOf(), dayjs().endOf('day').valueOf()];

const getTableColumns = (t: (key: string) => string) => [
  {
    title: t('common_id'),
    dataIndex: 'id'
  },
  {
    title: t('common_account'),
    dataIndex: 'account'
  },
  {
    title: t('pages_player_nickname'),
    dataIndex: 'name'
  },
  {
    title: t('pages_transaction_balanceAdjustment_currency'),
    dataIndex: 'currency',
    render: (currency: string) => {
      const option = CURRENCY_OPTIONS.find((option) => option.value === currency);
      return option ? t(option.label) : currency;
    }
  },
  {
    title: t('pages_transaction_balanceAdjustment_changeType'),
    dataIndex: 'changeType',
    render: (changeType: number) => {
      const option = CHANGE_TYPE_OPTIONS.find((option) => option.value === changeType);
      return option ? t(option.label) : changeType;
    }
  },
  {
    title: t('pages_transaction_balanceAdjustment_amount'),
    dataIndex: 'amount',
    render: (amount: number) => numberFormat(amount)
  },
  {
    title: t('pages_transaction_balanceAdjustment_beforeBalance'),
    dataIndex: 'beforeBalance',
    render: (beforeBalance: number) => numberFormat(beforeBalance)
  },
  {
    title: t('pages_transaction_balanceAdjustment_afterBalance'),
    dataIndex: 'afterBalance',
    render: (afterBalance: number) => numberFormat(afterBalance)
  },
  {
    title: t('common_note'),
    dataIndex: 'note'
  },
  {
    title: t('pages_transaction_balanceAdjustment_createdBy'),
    dataIndex: 'createdBy'
  },
  {
    title: t('pages_transaction_balanceAdjustment_createdAt'),
    dataIndex: 'createdAt',
    render: (createdAt: number) => formatTime(createdAt)
  }
];

const ConditionSearch = () => {
  const { t } = useTranslation();

  const translatedOptions = useMemo(
    () =>
      CONDITION_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <RForm.Item label={t('common_condition')}>
      <RForm.Item name="conditionType" className="inline-block !mr-2" initialValue="account">
        <RSelect options={translatedOptions} />
      </RForm.Item>
      <RForm.Item name="conditionValue" className="inline-block">
        <RInput className="max-h-[32px]" />
      </RForm.Item>
    </RForm.Item>
  );
};

const SearchFormWrap = ({
  onSearch,
  onReset
}: {
  onSearch: (values: Omit<BalanceAdjustmentSearchParams, 'page' | 'limit'>) => void;
  onReset: () => void;
}) => {
  const { t } = useTranslation();

  const handleSearch = (values: SearchFormValues) => {
    const { conditionType, conditionValue, start, end, ...rest } = values;
    const searchValues = {
      ...cleanSearchParams(rest),
      timeStart: start,
      timeEnd: end
    };

    if (conditionType && conditionValue) {
      onSearch({
        ...searchValues,
        [conditionType]: conditionValue
      });
    } else {
      onSearch(searchValues);
    }
  };

  const translatedChangeTypeOptions = useMemo(
    () =>
      CHANGE_TYPE_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  const translatedCurrencyOptions = useMemo(
    () =>
      CURRENCY_OPTIONS.map((option) => ({
        label: t(option.label),
        value: option.value
      })),
    [t]
  );

  return (
    <SearchForm<SearchFormValues> onSearch={handleSearch} onReset={onReset} className="">
      <RForm.Item name="date" label={t('pages_transaction_balanceAdjustment_timeSelect')}>
        <QuickDateSelect defaultActiveKey="today" />
      </RForm.Item>
      <ConditionSearch />
      <RForm.Item name="changeType" label={t('pages_transaction_balanceAdjustment_changeType')}>
        <RSelect
          options={translatedChangeTypeOptions}
          placeholder={t('common_please_select', {
            name: t('pages_transaction_balanceAdjustment_changeType')
          })}
        />
      </RForm.Item>
      <RForm.Item name="excludeTest" label={t('common_filtertest')} initialValue={1}>
        <RSelect
          options={[
            { label: t('common_yes'), value: 1 }, // 排除測試帳號
            { label: t('common_no'), value: 0 } // 包含全部
          ]}
        />
      </RForm.Item>
    </SearchForm>
  );
};

const BalanceAdjustmentPage = () => {
  const { t } = useTranslation();
  const { page, setPage, limit, setLimit } = usePagination({});
  const [params, setParams] = useState<BalanceAdjustmentSearchParams>({
    timeStart: defaultToday[0],
    timeEnd: defaultToday[1],
    excludeTest: 1,
    page,
    limit
  });

  const tableColumns = useMemo(() => getTableColumns(t), [t]);

  const { data, isPending } = useQuery({
    queryKey: ['balanceAdjustment', { page, limit, ...params }],
    queryFn: () =>
      getBalanceAdjustmentList({
        page,
        limit,
        ...params
      })
  });

  const handleChangePage = (page: number, pageSize: number) => {
    setPage(page);
    setLimit(pageSize);
  };

  const handleSearch = (values: SearchFormValues) => {
    setPage(1);
    setParams({
      ...params,
      ...values,
      page: 1,
      limit
    });
  };

  const handleReset = () => {
    setParams({
      timeStart: defaultToday[0],
      timeEnd: defaultToday[1],
      excludeTest: 1,
      page: 1,
      limit
    });
    setPage(1);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormWrap onSearch={handleSearch} onReset={handleReset} />}
    >
      <RTable
        loading={isPending}
        rowKey="id"
        dataSource={data?.data?.data || []}
        columns={tableColumns}
        pagination={{
          current: page,
          pageSize: limit,
          total: data?.data?.total || 0,
          showSizeChanger: true,
          onChange: handleChangePage
        }}
      />
    </TableSearchLayout>
  );
};

export default BalanceAdjustmentPage;
